#!/usr/bin/env pwsh
# Flutter智能构建脚本
# 自动检测是否需要清理，避免每次都clean

Write-Host "Flutter智能构建脚本" -ForegroundColor Green
Write-Host "===================" -ForegroundColor Green

# 检查Flutter环境
if (-not (Get-Command flutter -ErrorAction SilentlyContinue)) {
    Write-Host "错误: 未找到Flutter命令" -ForegroundColor Red
    exit 1
}

# 检查是否需要清理的函数
function Test-NeedClean {
    $needClean = $false
    $reasons = @()
    
    # 检查问题文件
    $problemFile = "build\app\intermediates\flutter\debug\flutter_assets\assets\images\svg\my\chart_down.svg"
    if (Test-Path $problemFile) {
        $needClean = $true
        $reasons += "检测到可能冲突的文件: chart_down.svg"
    }
    
    # 检查构建日志
    $logFiles = Get-ChildItem -Path "." -Name "flutter_*.log" -ErrorAction SilentlyContinue
    if ($logFiles.Count -gt 0) {
        $needClean = $true
        $reasons += "检测到构建错误日志"
    }
    
    # 检查build目录大小（如果太大可能有问题）
    if (Test-Path "build") {
        $buildSize = (Get-ChildItem -Path "build" -Recurse -ErrorAction SilentlyContinue | Measure-Object -Property Length -Sum).Sum
        if ($buildSize -gt 500MB) {
            $needClean = $true
            $reasons += "构建目录过大 ($([math]::Round($buildSize/1MB, 2))MB)"
        }
    }
    
    return @{
        NeedClean = $needClean
        Reasons = $reasons
    }
}

# 执行清理
function Invoke-Clean {
    Write-Host "执行清理操作..." -ForegroundColor Yellow
    
    try {
        # 停止可能的Flutter进程
        Get-Process -Name "flutter*" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
        
        # 清理Flutter缓存
        flutter clean
        
        # 删除可能的锁定文件
        Remove-Item -Path "pubspec.lock" -Force -ErrorAction SilentlyContinue
        
        Write-Host "清理完成" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "清理过程中出现错误: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 主构建流程
function Start-Build {
    param([bool]$ForceClean = $false)
    
    if ($ForceClean) {
        Write-Host "强制清理模式" -ForegroundColor Yellow
        if (-not (Invoke-Clean)) {
            return $false
        }
    } else {
        # 智能检测是否需要清理
        $cleanCheck = Test-NeedClean
        if ($cleanCheck.NeedClean) {
            Write-Host "需要清理，原因:" -ForegroundColor Yellow
            $cleanCheck.Reasons | ForEach-Object { Write-Host "  - $_" -ForegroundColor Yellow }
            
            if (-not (Invoke-Clean)) {
                return $false
            }
        } else {
            Write-Host "无需清理，直接构建" -ForegroundColor Green
        }
    }
    
    # 获取依赖
    Write-Host "获取依赖..." -ForegroundColor Cyan
    flutter pub get
    if ($LASTEXITCODE -ne 0) {
        Write-Host "获取依赖失败" -ForegroundColor Red
        return $false
    }
    
    # 开始构建
    Write-Host "开始构建..." -ForegroundColor Cyan
    flutter run
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "构建失败，尝试清理后重新构建..." -ForegroundColor Yellow
        if (Invoke-Clean) {
            flutter pub get
            flutter run
        }
    }
    
    return $LASTEXITCODE -eq 0
}

# 解析命令行参数
param(
    [switch]$Clean,
    [switch]$Help
)

if ($Help) {
    Write-Host @"
Flutter智能构建脚本

用法:
  .\build_smart.ps1          # 智能构建（自动检测是否需要清理）
  .\build_smart.ps1 -Clean   # 强制清理后构建
  .\build_smart.ps1 -Help    # 显示帮助

功能:
  - 自动检测是否需要清理构建缓存
  - 智能处理文件冲突问题
  - 构建失败时自动重试
"@
    exit 0
}

# 执行构建
$success = Start-Build -ForceClean:$Clean

if ($success) {
    Write-Host "构建成功！" -ForegroundColor Green
} else {
    Write-Host "构建失败！" -ForegroundColor Red
    exit 1
}
