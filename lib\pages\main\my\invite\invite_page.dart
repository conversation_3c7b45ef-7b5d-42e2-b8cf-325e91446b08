// // import 'package:barcode_scan2/barcode_scan2.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_starry_sky_box/model/user/invite_model.dart';
// import 'package:flutter_starry_sky_box/pages/main/my/invite/invite_controller.dart';
// import 'package:flutter_starry_sky_box/res/rc.dart';
// import 'package:flutter_starry_sky_box/res/styles.dart';
// import 'package:flutter_starry_sky_box/widget/image/custom_image.dart';
// import 'package:flutter_starry_sky_box/widget/load_state_layout.dart';
// import 'package:flutter_starry_sky_box/widget/screenutil/custom_screenutil.dart';
// import 'package:flutter_starry_sky_box/widget/sheet/share_sheet.dart';
// import 'package:flutter_starry_sky_box/widget/toast/custom_toast.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:get/get.dart';
// import 'package:pretty_qr_code/pretty_qr_code.dart';

// /// 邀请
// class InvitePage extends StatelessWidget {
//   const InvitePage({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return GetBuilder<InviteController>(
//       init: InviteController(),
//       builder: (inviteController) {
//         return Scaffold(
//           body: LoadStateLayout(
//             state: inviteController.state,
//             success: Stack(
//               children: [
//                 Container(
//                   width: double.infinity,
//                   height: double.infinity,
//                   decoration: const BoxDecoration(
//                     image: DecorationImage(image: AssetImage('assets/images/png/in_voick_bg.png'), fit: BoxFit.cover)
//                   ),
//                   child: SafeArea(
//                     child: Column(
//                       children: [
//                         SizedBox(height: 50.w,),

//                         Expanded(
//                           child: PageView(
//                             controller: inviteController.pageController,
//                             onPageChanged: (value) {
//                               inviteController.changeGuideIndex(value);
//                             },
//                             children: inviteController.inviteModel == null ? [] : inviteController.inviteModel!.poster!.map((e) => SizedBox(
//                               child: _buildShare(inviteController, e),
//                             )).toList(),
//                           ),
//                         ),

//                         SizedBox(height: 16.w,),

//                         Container(
//                           padding: EdgeInsets.only(left: 45.w, right: 14.w),
//                           child: Row(
//                             mainAxisAlignment: MainAxisAlignment.center,
//                             children: [
//                               SizedBox(
//                                 height: 4.w,
//                                 child: ListView.separated(
//                                   itemCount: inviteController.inviteModel?.poster?.length ?? 0,
//                                   scrollDirection: Axis.horizontal,
//                                   shrinkWrap: true,
//                                   physics: const NeverScrollableScrollPhysics(),
//                                   separatorBuilder: (context, index) {
//                                     return SizedBox(width: 7.w,);
//                                   },
//                                   itemBuilder: (BuildContext context, int index) {
//                                     if (index == inviteController.currentGuideIndex) {
//                                       return Container(
//                                         width: 15.w,
//                                         height: 4.w,
//                                         decoration: BoxDecoration(
//                                           borderRadius: BorderRadius.circular(4.r),
//                                           color: RC.black
//                                         ),
//                                       );
//                                     }
//                                     return Container(
//                                       width: 4.w,
//                                       height: 4.w,
//                                       decoration: BoxDecoration(
//                                         borderRadius: BorderRadius.circular(4.r),
//                                         color: RC.colorC3C5D2
//                                       ),
//                                     );
//                                   },
//                                 ),
//                               ),
//                             ],
//                           ),
//                         ),

//                         SizedBox(height: 15.w,),

//                         SizedBox(
//                           child: Row(
//                             mainAxisAlignment: MainAxisAlignment.spaceAround,
//                             children: [
//                               _buildIconText('copy_link'.tr, 'assets/images/svg/my/copy_link.svg', onTap: () {
//                                 // Clipboard.setData(ClipboardData(text: "${inviteController.inviteModel?.shareUrl}"));
//                                 // CustomToast.showTextToast('copy_success'.tr);
//                                 ShareSheet.show("${inviteController.inviteModel?.shareUrl}");
//                               }),
//                               _buildIconText('save_to_album'.tr, 'assets/images/svg/my/save_photo.svg', onTap: () {
//                                 inviteController.takeScreenshot();
//                               }),
//                               _buildIconText('send_to_friend'.tr, 'assets/images/svg/my/send_py.svg', onTap: () {
//                                 // Clipboard.setData(ClipboardData(text: "${inviteController.inviteModel?.shareUrl}"));
//                                 // CustomToast.showTextToast('copy_success'.tr);
//                                 ShareSheet.show("${inviteController.inviteModel?.shareUrl}");
//                               })
//                             ],
//                           ),
//                         ),

//                         SizedBox(height: 15.w,),

//                       ],
//                     ),
//                   ),
//                 ),

//                 Positioned(
//                   child: SafeArea(
//                     child: SizedBox(
//                       child: Row(
//                         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                         children: [
//                           IconButton(
//                             onPressed: () {
//                               Get.back();
//                             },
//                             icon: SvgPicture.asset('assets/images/svg/message/back.svg')
//                           ),

//                           // IconButton(
//                           //   onPressed: () async {
//                           //     final result = await BarcodeScanner.scan(
//                           //       options: ScanOptions(
//                           //         strings: {
//                           //           // 'cancel': _cancelController.text,
//                           //           // 'flash_on': _flashOnController.text,
//                           //           // 'flash_off': _flashOffController.text,
//                           //         },
//                           //         // restrictFormat: selectedFormats,
//                           //         // useCamera: _selectedCamera,
//                           //         // autoEnableFlash: _autoEnableFlash,
//                           //         android: AndroidOptions(
//                           //           // aspectTolerance: _aspectTolerance,
//                           //           // useAutoFocus: _useAutoFocus,
//                           //           appBarTitle: ''
//                           //         ),
//                           //       ),
//                           //     );
//                           //     // setState(() => scanResult = result);

//                           //     logger.d('${result.rawContent}');
//                           //   },
//                           //   icon: SvgPicture.asset('assets/images/svg/my/invork_scan.svg')
//                           // )
//                         ],
//                       ),
//                     )
//                   )
//                 )
//               ],
//             ),
//           ),
//         );
//       }
//     );
//   }

//   _buildShare(InviteController inviteController, Poster poster) {
//     return Container(
//       decoration: BoxDecoration(
//         borderRadius: BorderRadius.circular(15.r)
//       ),
//       padding: EdgeInsets.symmetric(horizontal: 15.w),
//       child: ClipRRect(
//         borderRadius: BorderRadius.circular(15.r),
//         child: RepaintBoundary(
//           key: poster.globalKey,
//           child: Container(
//             width: double.infinity,
//             height: double.infinity,
//             decoration: BoxDecoration(
//               color: RC.white,
//             ),
//             // decoration: BoxDecoration(
//             //   color: RC.white,
//             //   borderRadius: BorderRadius.circular(15.r)
//             // ),
//             // margin: EdgeInsets.symmetric(horizontal: 15.w),
//             padding: EdgeInsets.only(left: 11.w, top: 11.w, right: 11.w),
//             child: Column(
//                 children: [
//                   // Expanded(
//                   //   child: Container(
//                   //     width: double.infinity,
//                   //     decoration: BoxDecoration(
//                   //       borderRadius: BorderRadius.circular(15.r),
//                   //       // color: Colors.red,
//                   //     ),
//                   //     child: CustomImage(imageUrl: poster.image ?? ''),
//                   //   ),
//                   // ),

//                   SizedBox(
//                     child: Container(
//                       width: double.infinity,
//                       height: 451.w,
//                       decoration: BoxDecoration(
//                         borderRadius: BorderRadius.circular(15.r),
//                         // color: Colors.red,
//                       ),
//                       child: CustomImage(imageUrl: poster.image ?? ''),
//                     ),
//                   ),

//                   SizedBox(height: 30.w,),

//                   Container(
//                     padding: EdgeInsets.symmetric(horizontal: 12.w),
//                     child: Row(
//                       children: [
//                         Expanded(
//                           child: Row(
//                             children: [
//                               Container(
//                                 width: 48.w,
//                                 height: 48.w,
//                                 decoration: BoxDecoration(
//                                   borderRadius: BorderRadius.circular(48.r),
//                                   border: Border.all(
//                                     width: 1.w,
//                                     color: RC.black
//                                   )
//                                 ),
//                                 // child: Image.asset('assets/images/png/avatar.png'),
//                                 clipBehavior: Clip.hardEdge,
//                                 child: CustomImage(imageUrl: inviteController.inviteModel?.avatarThumb ?? '',),
//                               ),

//                               SizedBox(width: 11.w,),

//                               Column(
//                                 crossAxisAlignment: CrossAxisAlignment.start,
//                                 children: [
//                                   Text('${inviteController.inviteModel?.id}', style: Styles.font_custom(fontSize: 16.sp, fontWeight: FontWeight.w400, color: RC.black),),

//                                   SizedBox(height: 4.w,),

//                                   Row(
//                                     children: [
//                                       Text('${'invite_code'.tr}: ${inviteController.inviteModel?.code}', style: Styles.font_custom(fontSize: 12.sp, fontWeight: FontWeight.w400, color: RC.color3C3C3C),),

//                                       SizedBox(width: 4.w,),

//                                       GestureDetector(
//                                         behavior: HitTestBehavior.translucent,
//                                         onTap: () {
//                                           Clipboard.setData(ClipboardData(text: "${inviteController.inviteModel?.code}"));
//                                           CustomToast.showTextToast('copy_success'.tr);
//                                         },
//                                         child: SizedBox(
//                                           width: 9.w,
//                                           height: 9.w,
//                                           child: SvgPicture.asset('assets/images/svg/my/copy.svg'),
//                                         ),
//                                       )
//                                     ],
//                                   )
//                                 ],
//                               )
//                             ],
//                           ),
//                         ),

//                         SizedBox(
//                           width: 87.w,
//                           height: 87.w,
//                           // child: Image.asset('assets/images/png/qrcode.png'),
//                           child: PrettyQrView.data(
//                             // data: '${poster.url}',
//                             data: '${inviteController.inviteModel?.shareUrl}',
//                             errorCorrectLevel: QrErrorCorrectLevel.Q,
//                             decoration: PrettyQrDecoration(
//                               image: PrettyQrDecorationImage(
//                                 // image: AssetImage('images/flutter.png'),
//                                 // filterQuality: FilterQuality.high,
//                                 image: NetworkImage('${inviteController.inviteModel?.avatarThumb}')
//                               ),
//                             ),
//                           ),
//                         )
//                       ],
//                     ),
//                   ),

//                   // SizedBox(height: 10.w,),
//                 ],
//               ),
//           ),
//         ),
//       ),
//     );
//   }

//   _buildIconText(String text, String icon, {Function? onTap}) {
//     return GestureDetector(
//       behavior: HitTestBehavior.translucent,
//       onTap: () {
//         onTap?.call();
//       },
//       child: Column(
//         children: [
//           Container(
//             width: 54.w,
//             height: 54.w,
//             decoration: BoxDecoration(
//               color: RC.white,
//               borderRadius: BorderRadius.circular(54.r)
//             ),
//             padding: EdgeInsets.all(17.w),
//             child: SvgPicture.asset(icon),
//           ),
//           SizedBox(height: 6.w,),

//           Text(text, style: Styles.font_custom(fontSize: 12.sp, fontWeight: FontWeight.w400, color: RC.black),)
//         ],
//       ),
//     );
//   }

// }

// import 'package:barcode_scan2/barcode_scan2.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_starry_sky_box/pages/main/my/invite/invite_controller.dart';
import 'package:flutter_starry_sky_box/res/rc.dart';
import 'package:flutter_starry_sky_box/res/styles.dart';
import 'package:flutter_starry_sky_box/widget/image/custom_image.dart';
import 'package:flutter_starry_sky_box/widget/load_state_layout.dart';
import 'package:flutter_starry_sky_box/widget/screenutil/custom_screenutil.dart';
import 'package:flutter_starry_sky_box/widget/sheet/share_sheet.dart';
import 'package:flutter_starry_sky_box/widget/toast/custom_toast.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:pretty_qr_code/pretty_qr_code.dart';

import '../../../../model/user/invite_model/poster.dart';

/// 邀请
class InvitePage extends StatelessWidget {
  const InvitePage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<InviteController>(
        init: InviteController(),
        builder: (inviteController) {
          return Scaffold(
            body: LoadStateLayout(
              state: inviteController.state,
              success: Stack(
                children: [
                  Container(
                    width: double.infinity,
                    height: double.infinity,
                    decoration: const BoxDecoration(
                        image: DecorationImage(
                            image:
                                AssetImage('assets/images/png/in_voick_bg.png'),
                            fit: BoxFit.cover)),
                    child: SafeArea(
                      child: Column(
                        children: [
                          SizedBox(
                            height: 50.w,
                          ),
                          Expanded(
                            child: Container(
                              width: double.infinity,
                              height: double.infinity,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(15.r)),
                              padding: EdgeInsets.symmetric(horizontal: 15.w),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(15.r),
                                child: RepaintBoundary(
                                  key: inviteController.globalKey,
                                  child: Container(
                                    width: double.infinity,
                                    height: double.infinity,
                                    decoration: BoxDecoration(
                                      color: RC.white,
                                    ),
                                    padding: EdgeInsets.only(
                                        left: 11.w, top: 11.w, right: 11.w),
                                    child: Column(
                                      children: [
                                        Expanded(
                                          child: SizedBox(
                                            child: Container(
                                              width: double.infinity,
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(15.r),
                                              ),
                                              child: Column(
                                                children: [
                                                  Expanded(
                                                    child: PageView(
                                                      controller:
                                                          inviteController
                                                              .pageController,
                                                      onPageChanged: (value) {
                                                        inviteController
                                                            .changeGuideIndex(
                                                                value);
                                                      },
                                                      children: inviteController
                                                                  .inviteModel ==
                                                              null
                                                          ? []
                                                          : inviteController
                                                              .inviteModel!
                                                              .poster!
                                                              .map(
                                                                  (e) =>
                                                                      SizedBox(
                                                                        child:
                                                                            SizedBox(
                                                                          width:
                                                                              double.infinity,
                                                                          height:
                                                                              double.infinity,
                                                                          child:
                                                                              CustomImage(
                                                                            imageUrl:
                                                                                e.image ?? '',
                                                                            fit:
                                                                                BoxFit.contain,
                                                                          ),
                                                                        ),
                                                                      ))
                                                              .toList(),
                                                    ),
                                                  )
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                        SizedBox(
                                          height: 15.w,
                                        ),
                                        SizedBox(
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              SizedBox(
                                                height: 4.w,
                                                child: ListView.separated(
                                                  itemCount: inviteController
                                                          .inviteModel
                                                          ?.poster
                                                          ?.length ??
                                                      0,
                                                  scrollDirection:
                                                      Axis.horizontal,
                                                  shrinkWrap: true,
                                                  physics:
                                                      const NeverScrollableScrollPhysics(),
                                                  separatorBuilder:
                                                      (context, index) {
                                                    return SizedBox(
                                                      width: 7.w,
                                                    );
                                                  },
                                                  itemBuilder:
                                                      (BuildContext context,
                                                          int index) {
                                                    if (index ==
                                                        inviteController
                                                            .currentGuideIndex) {
                                                      return Container(
                                                        width: 15.w,
                                                        height: 4.w,
                                                        decoration: BoxDecoration(
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        4.r),
                                                            color: RC.black),
                                                      );
                                                    }
                                                    return Container(
                                                      width: 4.w,
                                                      height: 4.w,
                                                      decoration: BoxDecoration(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(
                                                                      4.r),
                                                          color:
                                                              RC.colorC3C5D2),
                                                    );
                                                  },
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        Container(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 12.w, vertical: 12.w),
                                          child: Row(
                                            children: [
                                              Expanded(
                                                child: Row(
                                                  children: [
                                                    Container(
                                                      width: 48.w,
                                                      height: 48.w,
                                                      decoration: BoxDecoration(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(
                                                                      48.r),
                                                          border: Border.all(
                                                              width: 1.w,
                                                              color: RC.black)),
                                                      clipBehavior:
                                                          Clip.hardEdge,
                                                      child: CustomImage(
                                                        imageUrl: inviteController
                                                                .inviteModel
                                                                ?.avatarThumb ??
                                                            '',
                                                      ),
                                                    ),
                                                    SizedBox(
                                                      width: 11.w,
                                                    ),
                                                    Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        Text(
                                                          '${inviteController.inviteModel?.id}',
                                                          style: Styles
                                                              .font_custom(
                                                                  fontSize:
                                                                      16.sp,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w400,
                                                                  color:
                                                                      RC.black),
                                                        ),
                                                        SizedBox(
                                                          height: 4.w,
                                                        ),
                                                        Row(
                                                          children: [
                                                            Text(
                                                              '${'invite_code'.tr}: ${inviteController.inviteModel?.code}',
                                                              style: Styles.font_custom(
                                                                  fontSize:
                                                                      12.sp,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w400,
                                                                  color: RC
                                                                      .color3C3C3C),
                                                            ),
                                                            SizedBox(
                                                              width: 4.w,
                                                            ),
                                                            GestureDetector(
                                                              behavior:
                                                                  HitTestBehavior
                                                                      .translucent,
                                                              onTap: () {
                                                                Clipboard.setData(
                                                                    ClipboardData(
                                                                        text:
                                                                            "${inviteController.inviteModel?.code}"));
                                                                CustomToast
                                                                    .showTextToast(
                                                                        'copy_success'
                                                                            .tr);
                                                              },
                                                              child: SizedBox(
                                                                width: 9.w,
                                                                height: 9.w,
                                                                child: SvgPicture
                                                                    .asset(
                                                                        'assets/images/svg/my/copy.svg'),
                                                              ),
                                                            )
                                                          ],
                                                        )
                                                      ],
                                                    )
                                                  ],
                                                ),
                                              ),
                                              SizedBox(
                                                width: 80.w,
                                                height: 80.w,
                                                child: PrettyQrView.data(
                                                  data:
                                                      '${inviteController.inviteModel?.shareUrl}',
                                                  errorCorrectLevel:
                                                      QrErrorCorrectLevel.Q,
                                                  decoration:
                                                      PrettyQrDecoration(
                                                    image: PrettyQrDecorationImage(
                                                        image: NetworkImage(
                                                            '${inviteController.inviteModel?.avatarThumb}')),
                                                  ),
                                                ),
                                              )
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                          SizedBox(
                            height: 15.w,
                          ),
                          SizedBox(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              children: [
                                _buildIconText('copy_link'.tr,
                                    'assets/images/svg/my/copy_link.svg',
                                    onTap: () {
                                  ShareSheet.show(
                                      "${inviteController.inviteModel?.shareUrl}");
                                }),
                                _buildIconText('save_to_album'.tr,
                                    'assets/images/svg/my/save_photo.svg',
                                    onTap: () {
                                  inviteController.takeScreenshot();
                                }),
                                _buildIconText('send_to_friend'.tr,
                                    'assets/images/svg/my/send_py.svg',
                                    onTap: () {
                                  ShareSheet.show(
                                      "${inviteController.inviteModel?.shareUrl}");
                                })
                              ],
                            ),
                          ),
                          SizedBox(
                            height: 15.w,
                          ),
                        ],
                      ),
                    ),
                  ),
                  Positioned(
                      child: SafeArea(
                          child: SizedBox(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        IconButton(
                            onPressed: () {
                              Get.back();
                            },
                            icon: SvgPicture.asset(
                                'assets/images/svg/message/back.svg')),
                      ],
                    ),
                  )))
                ],
              ),
            ),
          );
        });
  }

  _buildShare(InviteController inviteController, Poster poster) {
    return Container(
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(15.r)),
      padding: EdgeInsets.symmetric(horizontal: 15.w),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(15.r),
        child: RepaintBoundary(
          key: poster.globalKey,
          child: Container(
            width: double.infinity,
            height: double.infinity,
            decoration: BoxDecoration(
              color: RC.white,
            ),
            padding: EdgeInsets.only(left: 11.w, top: 11.w, right: 11.w),
            child: Column(
              children: [
                Expanded(
                  child: SizedBox(
                    child: Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15.r),
                      ),
                      child: CustomImage(imageUrl: poster.image ?? ''),
                    ),
                  ),
                ),
                Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.w),
                  child: Row(
                    children: [
                      Expanded(
                        child: Row(
                          children: [
                            Container(
                              width: 48.w,
                              height: 48.w,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(48.r),
                                  border:
                                      Border.all(width: 1.w, color: RC.black)),
                              clipBehavior: Clip.hardEdge,
                              child: CustomImage(
                                imageUrl:
                                    inviteController.inviteModel?.avatarThumb ??
                                        '',
                              ),
                            ),
                            SizedBox(
                              width: 11.w,
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '${inviteController.inviteModel?.id}',
                                  style: Styles.font_custom(
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.w400,
                                      color: RC.black),
                                ),
                                SizedBox(
                                  height: 4.w,
                                ),
                                Row(
                                  children: [
                                    Text(
                                      '${'invite_code'.tr}: ${inviteController.inviteModel?.code}',
                                      style: Styles.font_custom(
                                          fontSize: 12.sp,
                                          fontWeight: FontWeight.w400,
                                          color: RC.color3C3C3C),
                                    ),
                                    SizedBox(
                                      width: 4.w,
                                    ),
                                    GestureDetector(
                                      behavior: HitTestBehavior.translucent,
                                      onTap: () {
                                        Clipboard.setData(ClipboardData(
                                            text:
                                                "${inviteController.inviteModel?.code}"));
                                        CustomToast.showTextToast(
                                            'copy_success'.tr);
                                      },
                                      child: SizedBox(
                                        width: 9.w,
                                        height: 9.w,
                                        child: SvgPicture.asset(
                                            'assets/images/svg/my/copy.svg'),
                                      ),
                                    )
                                  ],
                                )
                              ],
                            )
                          ],
                        ),
                      ),
                      SizedBox(
                        width: 87.w,
                        height: 87.w,
                        child: PrettyQrView.data(
                          data: '${inviteController.inviteModel?.shareUrl}',
                          errorCorrectLevel: QrErrorCorrectLevel.Q,
                          decoration: PrettyQrDecoration(
                            image: PrettyQrDecorationImage(
                                image: NetworkImage(
                                    '${inviteController.inviteModel?.avatarThumb}')),
                          ),
                        ),
                      )
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  _buildIconText(String text, String icon, {Function? onTap}) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        onTap?.call();
      },
      child: Column(
        children: [
          Container(
            width: 54.w,
            height: 54.w,
            decoration: BoxDecoration(
                color: RC.white, borderRadius: BorderRadius.circular(54.r)),
            padding: EdgeInsets.all(17.w),
            child: SvgPicture.asset(icon),
          ),
          SizedBox(
            height: 6.w,
          ),
          Text(
            text,
            style: Styles.font_custom(
                fontSize: 12.sp, fontWeight: FontWeight.w400, color: RC.black),
          )
        ],
      ),
    );
  }
}
