import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_starry_sky_box/widget/screenutil/custom_screenutil.dart';
import 'package:shimmer/shimmer.dart';

import '../../utils/color_util.dart';

class CustomImage extends StatelessWidget {
  const CustomImage({
    super.key,
    this.imageUrl = '',
    this.placeholderHeight,
    this.placeholderWidth,
    this.size,
    this.fit = BoxFit.cover,
  });

  final String imageUrl;
  final double? placeholderHeight;
  final double? placeholderWidth;
  final double? size;
  final BoxFit fit;

  @override
  Widget build(BuildContext context) {
    return CachedNetworkImage(
      imageUrl: imageUrl,
      fit: fit,
      placeholder: (_, string) {
        return Shimmer.fromColors(
          baseColor: ColorUtil.hexToColor('#111111'),
          highlightColor: const Color.fromARGB(255, 44, 44, 44),
          child: Container(
            color: ColorUtil.hexToColor('#111111'),
          ),
        );
      },
      placeholderFadeInDuration: const Duration(microseconds: 100),
      errorWidget: (context, url, error) {
        return Container(
          padding: EdgeInsets.all(4.w),
          width: double.infinity,
          height: double.infinity,
          child: Image.asset(
            'assets/images/png/error.png',
            fit: BoxFit.cover,
          ),
        );
      },
    );
  }
}
