org.gradle.jvmargs=-Xmx4G
android.useAndroidX=true
android.enableJetifier=true

# 优化构建性能和缓存处理
org.gradle.parallel=true
org.gradle.daemon=true
org.gradle.configureondemand=true
org.gradle.caching=true

# 解决文件覆盖问题
android.enableBuildCache=false
android.enableD8.desugaring=true

# Windows文件系统优化
org.gradle.vfs.watch=false

# 解决资源文件复制冲突
android.enableResourceOptimizations=true

# 增量构建优化
org.gradle.configuration-cache.problems=warn

# 文件系统监控优化
org.gradle.vfs.verbose=false
org.gradle.workers.max=4